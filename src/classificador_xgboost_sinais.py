#!/usr/bin/env python3
"""
Script para classificação de sinais de compra e venda usando XGBoost
Baseado no pct_change da média OHLC das ações diversificadas com sinais futuros
Usa features: pct_change da média OHLC (dias passados), volume, spread, volatilidade
e features econométricas baseadas em dados OHLCV (Parkinson, MFI, EMV,
Amihud, Roll Spread, Hurst, CMF, A/D Line, Volume Oscillator, etc.)
"""

import yfinance as yf
import matplotlib.pyplot as plt
import pandas as pd
import numpy as np
import os
import sys
from datetime import datetime, timedelta
import xgboost as xgb
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.metrics import classification_report, confusion_matrix, accuracy_score
from sklearn.preprocessing import StandardScaler
import seaborn as sns
import pickle
import warnings
warnings.filterwarnings('ignore')

# Adicionar o diretório src ao path para importar functions e config
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from functions import edge_rolling
from config_loader import config, setup_environment

def carregar_acoes_diversificadas():
    """
    Carrega todas as ações do arquivo CSV de diversificação usando configuração
    """
    try:
        # Usar configuração ao invés de hardcode
        file_paths = config.get_file_paths()
        csv_path = file_paths['acoes_diversificacao']
        df = pd.read_csv(csv_path)

        # Pegar todas as ações (excluindo linhas vazias)
        acoes = []
        for _, row in df.iterrows():
            if pd.notna(row['Ticker']) and row['Ticker'].strip():
                ticker = row['Ticker'] + '.SA'
                nome = row['Nome']
                acoes.append((ticker, nome))

        print(f"📋 Carregadas {len(acoes)} ações diversificadas do arquivo: {csv_path}")
        return acoes

    except Exception as e:
        print(f"❌ Erro ao carregar arquivo CSV: {e}")
        return []

def baixar_dados_acao(ticker, nome):
    """
    Baixa dados históricos de uma ação usando configuração
    """
    try:
        # Usar configuração para período de dados do XGBoost
        periodo = config.get('xgboost.data_period')
        print(f"     📊 Baixando dados de {ticker} ({nome}) - período: {periodo}")

        # Baixar dados históricos
        dados = yf.download(ticker, period=periodo, progress=False)

        if dados.empty:
            print(f"     ❌ Nenhum dado encontrado para {ticker}")
            return None

        # Corrigir MultiIndex se necessário
        if isinstance(dados.columns, pd.MultiIndex):
            dados.columns = dados.columns.droplevel(1)

        # Verificar se tem dados suficientes
        if len(dados) < 100:
            print(f"     ⚠️ Poucos dados para {ticker}: {len(dados)} dias")
            return None

        return dados

    except Exception as e:
        print(f"     ❌ Erro ao baixar dados de {ticker}: {e}")
        return None

def corrigir_valores_zero_ultimo_dia(dados):
    """
    Corrige valores zero no último dia substituindo pelo penúltimo dia
    """
    if len(dados) < 2:
        return dados

    # Colunas OHLC para verificar
    colunas_ohlc = ['Open', 'High', 'Low', 'Close']

    # Verificar e corrigir valores zero no último dia
    for coluna in colunas_ohlc:
        if coluna in dados.columns:
            # Obter o último valor como escalar
            ultimo_valor = dados[coluna].iloc[-1]
            if hasattr(ultimo_valor, 'item'):
                ultimo_valor = ultimo_valor.item()

            # Verificar se é zero ou NaN
            if pd.isna(ultimo_valor) or ultimo_valor == 0:
                # Substituir pelo valor do penúltimo dia
                penultimo_valor = dados[coluna].iloc[-2]
                if hasattr(penultimo_valor, 'item'):
                    penultimo_valor = penultimo_valor.item()

                dados.loc[dados.index[-1], coluna] = penultimo_valor
                print(f"     🔧 Corrigido valor zero em {coluna}: {ultimo_valor} → {penultimo_valor}")

    return dados

def calcular_features_econometricas_ohlcv(dados):
    """
    Calcula features econométricas avançadas usando dados OHLCV
    Baseado nas features descritas pelo usuário (excluindo RSI conforme solicitado)

    SIMULAÇÃO REALÍSTICA: Para cada dia durante a estimativa ao longo do ano,
    não usa o valor de Close do próprio dia (evita data leakage)
    """
    try:
        # SIMULAÇÃO REALÍSTICA: Criar price_reference sem usar Close do próprio dia
        # Para cada dia i, usar apenas informações disponíveis até o dia i-1
        price_reference = dados['Close'].copy()

        # Para simulação realística: substituir Close de cada dia por OHL
        # (simula que Close não está disponível durante o trading do dia)
        for i in range(1, len(dados)):  # Começar do dia 1 (dia 0 pode usar Close histórico)
            price_reference.iloc[i] = (
                dados['Open'].iloc[i] + dados['High'].iloc[i] + dados['Low'].iloc[i]
            ) / 3
        # 1. Volatilidade de Parkinson (usa High-Low range)
        dados['Parkinson_Volatility'] = np.sqrt((1 / (4 * np.log(2))) *
                                              ((np.log(dados['High'] / dados['Low'])**2)))

        # 2. Volatilidade de Garman-Klass - REMOVIDA (alta correlação com Parkinson)
        # dados['GK_Volatility'] = np.sqrt(0.5 * np.log(dados['High']/dados['Low'])**2 -
        #                                (2*np.log(2)-1) * np.log(dados['Close']/dados['Open'])**2)

        # 3. Índice de Fluxo de Dinheiro (MFI - Money Flow Index)
        typical_price = (dados['High'] + dados['Low'] + price_reference) / 3
        money_flow = typical_price * dados['Volume']
        delta_tp = typical_price.diff()
        positive_flow = money_flow.where(delta_tp > 0, 0).rolling(window=14).sum()
        negative_flow = money_flow.where(delta_tp < 0, 0).rolling(window=14).sum()
        mf_ratio = positive_flow / negative_flow
        dados['MFI'] = 100 - (100 / (1 + mf_ratio))

        # 4. Índice de Facilidade de Movimento (EMV - Ease of Movement)
        distance_moved = ((dados['High'] + dados['Low']) / 2) - ((dados['High'].shift(1) + dados['Low'].shift(1)) / 2)
        emv_divisor = config.get('xgboost.calculations.emv_volume_divisor', 1000000)
        box_ratio = (dados['Volume'] / emv_divisor) / (dados['High'] - dados['Low'])
        dados['EMV'] = distance_moved / box_ratio
        dados['EMV_MA'] = dados['EMV'].rolling(window=14).mean()

        # 5. Índice de Amihud (Illiquidity)
        dados['Amihud'] = abs(price_reference.pct_change()) / dados['Volume']

        # 6. Roll Spread (estimativa de bid-ask spread)
        price_change = price_reference.diff()
        cov = price_change.rolling(window=20).apply(
            lambda x: np.cov(x[:-1], x[1:])[0, 1] if len(x) > 1 else 0, raw=False)
        dados['Roll_Spread'] = 2 * np.sqrt(np.abs(cov))  # Usar valor absoluto para evitar NaN

        # 7. Índice de Hurst (versão simplificada mais robusta)
        def hurst_simple(ts):
            try:
                if len(ts) < 20:
                    return np.nan
                # Usar método R/S simplificado
                ts = np.array(ts)
                ts = ts[~np.isnan(ts)]  # Remover NaN
                if len(ts) < 20:
                    return np.nan

                # Calcular retornos
                returns = np.diff(np.log(ts))
                if len(returns) < 10:
                    return np.nan

                # Calcular R/S para diferentes lags
                lags = [5, 10, 15]
                rs_values = []

                for lag in lags:
                    if len(returns) < lag:
                        continue
                    # Dividir em blocos
                    n_blocks = len(returns) // lag
                    if n_blocks < 2:
                        continue

                    rs_block = []
                    for i in range(n_blocks):
                        block = returns[i*lag:(i+1)*lag]
                        if len(block) < lag:
                            continue

                        # Calcular R/S para este bloco
                        mean_block = np.mean(block)
                        deviations = np.cumsum(block - mean_block)
                        R = np.max(deviations) - np.min(deviations)
                        S = np.std(block)

                        if S > 0:
                            rs_block.append(R / S)

                    if rs_block:
                        rs_values.append(np.mean(rs_block))

                if len(rs_values) >= 2:
                    # Estimar H usando regressão simples
                    log_lags = np.log(lags[:len(rs_values)])
                    log_rs = np.log(rs_values)
                    if len(log_lags) >= 2:
                        H = np.polyfit(log_lags, log_rs, 1)[0]
                        return max(0, min(1, H))  # Limitar entre 0 e 1

                return 0.5  # Valor neutro se não conseguir calcular
            except:
                return 0.5

        dados['Hurst'] = price_reference.rolling(window=50).apply(
            lambda x: hurst_simple(x), raw=True)

        # 8. Volatilidade Histórica Normalizada pelo Volume
        vol_returns = price_reference.pct_change().rolling(window=20).std()
        vol_volume = dados['Volume'].rolling(window=20).mean() ** 0.5
        # Evitar divisão por zero
        vol_volume = vol_volume.replace(0, np.nan)
        dados['Vol_per_Volume'] = vol_returns / vol_volume

        # 9. Índice de Chaikin Money Flow (CMF)
        # Evitar divisão por zero no denominador
        high_low_diff = dados['High'] - dados['Low']
        high_low_diff = high_low_diff.replace(0, np.nan)

        money_flow_multiplier = ((price_reference - dados['Low']) - (dados['High'] - price_reference)) / high_low_diff
        money_flow_volume = money_flow_multiplier * dados['Volume']
        volume_sum = dados['Volume'].rolling(window=20).sum()
        volume_sum = volume_sum.replace(0, np.nan)
        dados['CMF'] = money_flow_volume.rolling(window=20).sum() / volume_sum

        # 10. Índice de Acumulação/Distribuição (A/D)
        # Evitar divisão por zero
        high_low_diff = dados['High'] - dados['Low']
        high_low_diff = high_low_diff.replace(0, np.nan)

        ad = ((price_reference - dados['Low']) - (dados['High'] - price_reference)) / high_low_diff * dados['Volume']
        dados['AD_Line'] = ad.cumsum()

        # 11. Oscilador de Volume (VO)
        vol_ma_10 = dados['Volume'].rolling(window=10).mean()
        vol_ma_10 = vol_ma_10.replace(0, np.nan)
        dados['VO'] = (dados['Volume'].rolling(window=5).mean() / vol_ma_10 - 1) * 100

        # Preencher valores infinitos e NaN com valores apropriados
        econometric_features = ['Parkinson_Volatility', 'MFI', 'EMV', 'EMV_MA',
                               'Amihud', 'Roll_Spread', 'Hurst', 'Vol_per_Volume', 'CMF', 'AD_Line', 'VO']

        for feature in econometric_features:
            if feature in dados.columns:
                # Substituir infinitos por NaN
                dados[feature] = dados[feature].replace([np.inf, -np.inf], np.nan)
                # Preencher NaN com mediana da série
                dados[feature] = dados[feature].fillna(dados[feature].median())

        return dados

    except Exception as e:
        print(f"     ⚠️ Erro ao calcular features econométricas: {e}")
        return dados

def calcular_features_e_sinais(dados, ticker=None, tickers_carteira=None):
    """
    Calcula features e sinais de compra/venda baseados no pct_change da média OHLC usando configuração
    Sinais são gerados para todas as ações (para comparação justa entre métodos)
    Agora inclui features econométricas baseadas em dados OHLCV

    SIMULAÇÃO REALÍSTICA: Para cada dia durante a estimativa ao longo do ano,
    não usa o valor de Close do próprio dia (evita data leakage)
    """
    # Corrigir valores zero no último dia
    dados = corrigir_valores_zero_ultimo_dia(dados)

    # SIMULAÇÃO REALÍSTICA: Calcular média OHLC sem usar Close do próprio dia
    # Para cada dia i, usar apenas informações disponíveis até o dia i-1
    dados['Media_OHLC'] = np.nan

    for i in range(len(dados)):
        if i == 0:
            # Primeiro dia: usar OHLC completo (não há predição para fazer)
            dados['Media_OHLC'].iloc[i] = (
                dados['Open'].iloc[i] + dados['High'].iloc[i] +
                dados['Low'].iloc[i] + dados['Close'].iloc[i]
            ) / 4
        else:
            # Dias subsequentes: simular trading realístico
            # Para predição do dia i, usar apenas OHL do dia i (Close não disponível durante o dia)
            dados['Media_OHLC'].iloc[i] = (
                dados['Open'].iloc[i] + dados['High'].iloc[i] + dados['Low'].iloc[i]
            ) / 3

    # Calcular pct_change da média OHLC (variação percentual em relação ao dia anterior)
    dados['Media_OHLC_PctChange'] = dados['Media_OHLC'].pct_change()

    # Usar configurações do XGBoost
    volatility_window = config.get('xgboost.features.volatility_window')
    signal_horizon = config.get('xgboost.signal_horizon')
    ohlc_lags = config.get('xgboost.features.ohlc_lags')
    econometric_lags = config.get('xgboost.features.econometric_lags')

    # Calcular volatilidade usando janela configurada
    returns = dados['Media_OHLC'].pct_change()
    dados['Volatilidade'] = returns.rolling(window=volatility_window).std() * 100

    # Calcular spread bid-ask usando a função edge_rolling
    try:
        spread_series = edge_rolling(dados, window=volatility_window)
        dados['Spread'] = spread_series * 100  # Converter para percentual
    except Exception as e:
        print(f"     ⚠️ Erro ao calcular spread para {ticker}: {e}")
        # Fallback: usar uma estimativa simples baseada na volatilidade intraday
        high_low_spread = ((dados['High'] - dados['Low']) / dados['Media_OHLC']) * 100
        dados['Spread'] = high_low_spread.rolling(window=volatility_window).mean()

    # Preencher NaN
    dados['Spread'] = dados['Spread'].fillna(dados['Spread'].mean())
    dados['Volatilidade'] = dados['Volatilidade'].fillna(dados['Volatilidade'].mean())

    # Calcular features econométricas baseadas em OHLCV
    print(f"     🔬 Calculando features econométricas para {ticker}...")
    dados = calcular_features_econometricas_ohlcv(dados)

    # Criar sinais futuros usando horizonte configurado
    dados['Media_OHLC_Futura'] = dados['Media_OHLC'].shift(-signal_horizon)

    # Sinal de compra: média OHLC atual < média OHLC futura (para todas as ações)
    dados['Sinal_Compra'] = (dados['Media_OHLC'] < dados['Media_OHLC_Futura']).astype(int)

    # Sinal de venda: média OHLC atual > média OHLC futura (para todas as ações)
    dados['Sinal_Venda'] = (dados['Media_OHLC'] > dados['Media_OHLC_Futura']).astype(int)

    # Criar features de pct_change da média OHLC passada usando configuração
    for i in range(1, ohlc_lags + 1):
        dados[f'Media_OHLC_PctChange_Lag_{i}'] = dados['Media_OHLC_PctChange'].shift(i)

    # Criar features lagged das variáveis econométricas
    econometric_features = [
        'Volume', 'Spread', 'Volatilidade',
        'Parkinson_Volatility', 'MFI', 'EMV', 'EMV_MA',
        'Amihud', 'Roll_Spread', 'Hurst', 'Vol_per_Volume', 'CMF', 'AD_Line', 'VO'
    ]

    print(f"     🔄 Criando {econometric_lags} lags para features econométricas...")
    for feature in econometric_features:
        if feature in dados.columns:
            for i in range(1, econometric_lags + 1):
                dados[f'{feature}_Lag_{i}'] = dados[feature].shift(i)

    # Remover linhas com NaN (início e fim da série)
    dados = dados.dropna()

    return dados

def preparar_dataset(acoes_dados):
    """
    Prepara dataset combinado de todas as ações para treinamento
    Preserva o índice de data para divisão temporal
    Cria target binário: 0=Venda, 1=Compra (elimina classe "Sem Ação")
    """
    datasets = []

    for ticker, dados in acoes_dados.items():
        if dados is not None and len(dados) > 50:
            # Adicionar coluna do ticker para identificação
            dados_copy = dados.copy()
            dados_copy['Ticker'] = ticker
            # Resetar índice para preservar as datas como coluna
            dados_copy = dados_copy.reset_index()
            datasets.append(dados_copy)

    if not datasets:
        print("❌ Nenhum dataset válido encontrado")
        return None, None, None, None

    # Combinar todos os datasets preservando as datas
    dataset_completo = pd.concat(datasets, ignore_index=True)

    # Converter coluna Date para datetime se necessário
    if 'Date' in dataset_completo.columns:
        dataset_completo['Date'] = pd.to_datetime(dataset_completo['Date'])
    elif dataset_completo.index.name == 'Date' or isinstance(dataset_completo.index, pd.DatetimeIndex):
        dataset_completo = dataset_completo.reset_index()
        dataset_completo['Date'] = pd.to_datetime(dataset_completo['Date'])

    # Features: pct_change da média OHLC passada (configurável), volume, spread, volatilidade + features econométricas + lags
    ohlc_lags = config.get('xgboost.features.ohlc_lags')
    econometric_lags = config.get('xgboost.features.econometric_lags')

    # Features básicas (incluindo features do dia atual)
    basic_features = [f'Media_OHLC_PctChange_Lag_{i}' for i in range(1, ohlc_lags + 1)] + ['Volume', 'Spread', 'Volatilidade']

    # Features econométricas do dia atual (excluindo GK_Volatility devido à alta correlação)
    econometric_features_current = [
        'Parkinson_Volatility', 'MFI', 'EMV', 'EMV_MA',
        'Amihud', 'Roll_Spread', 'Hurst', 'Vol_per_Volume', 'CMF', 'AD_Line', 'VO'
    ]

    # Features econométricas lagged (incluindo Volume, Spread, Volatilidade)
    econometric_features_all = [
        'Volume', 'Spread', 'Volatilidade',
        'Parkinson_Volatility', 'MFI', 'EMV', 'EMV_MA',
        'Amihud', 'Roll_Spread', 'Hurst', 'Vol_per_Volume', 'CMF', 'AD_Line', 'VO'
    ]

    econometric_features_lagged = []
    for feature in econometric_features_all:
        for i in range(1, econometric_lags + 1):
            econometric_features_lagged.append(f'{feature}_Lag_{i}')

    # Combinar todas as features
    feature_cols = basic_features + econometric_features_current + econometric_features_lagged

    # Verificar se todas as colunas existem
    colunas_existentes = [col for col in feature_cols if col in dataset_completo.columns]
    if len(colunas_existentes) != len(feature_cols):
        print(f"⚠️ Algumas features não encontradas. Disponíveis: {len(colunas_existentes)}/{len(feature_cols)}")
        feature_cols = colunas_existentes

    X = dataset_completo[feature_cols]

    # Criar target binário: 0=Venda, 1=Compra (eliminar classe "Sem Ação")
    # Filtrar apenas registros que têm sinal de compra OU venda (eliminar "sem ação")
    tem_sinal_mask = (dataset_completo['Sinal_Compra'] == 1) | (dataset_completo['Sinal_Venda'] == 1)

    # Filtrar dataset para incluir apenas registros com sinais
    dataset_filtrado = dataset_completo[tem_sinal_mask].copy()
    X_filtrado = X[tem_sinal_mask].copy()

    # Criar target binário: 1=Compra, 0=Venda
    y_binary = dataset_filtrado['Sinal_Compra'].astype(int)

    # Estatísticas das classes
    unique, counts = np.unique(y_binary, return_counts=True)
    class_stats = dict(zip(unique, counts))

    print(f"📊 Dataset preparado (modelo binário - sem classe 'Sem Ação'):")
    print(f"   • Total de amostras: {len(X_filtrado)} (filtradas de {len(X)} originais)")
    print(f"   • Features: {len(feature_cols)}")
    print(f"   • Colunas de features: {feature_cols}")
    print(f"   • Classe 0 (Venda): {class_stats.get(0, 0)} ({class_stats.get(0, 0)/len(y_binary)*100:.1f}%)")
    print(f"   • Classe 1 (Compra): {class_stats.get(1, 0)} ({class_stats.get(1, 0)/len(y_binary)*100:.1f}%)")

    return X_filtrado, y_binary, feature_cols, dataset_filtrado

def dividir_dados_temporal(dataset_completo, feature_cols, y_binary):
    """
    Divide os dados temporalmente baseado na configuração
    Trabalha com target binário
    """
    if 'Date' not in dataset_completo.columns:
        print("❌ Coluna 'Date' não encontrada. Não é possível fazer divisão temporal.")
        return None, None, None, None

    # Ordenar por data
    dataset_ordenado = dataset_completo.sort_values('Date').copy()
    y_ordenado = y_binary[dataset_ordenado.index]

    # Obter configurações de divisão temporal
    test_years = config.get('xgboost.temporal_split.test_years', 1)
    train_years = config.get('xgboost.temporal_split.train_years', 4)

    # Encontrar data de corte baseada na configuração
    data_max = dataset_ordenado['Date'].max()
    data_corte = data_max - pd.DateOffset(years=test_years)

    print(f"📅 Divisão temporal dos dados:")
    print(f"   • Data máxima: {data_max.strftime('%Y-%m-%d')}")
    print(f"   • Data de corte: {data_corte.strftime('%Y-%m-%d')}")
    print(f"   • Configuração: {train_years} anos treino, {test_years} ano(s) teste")

    # Dividir dados
    mask_treino = dataset_ordenado['Date'] <= data_corte
    mask_teste = dataset_ordenado['Date'] > data_corte

    dados_treino = dataset_ordenado[mask_treino]
    dados_teste = dataset_ordenado[mask_teste]

    print(f"   • Dados de treino: {len(dados_treino)} registros ({dados_treino['Date'].min().strftime('%Y-%m-%d')} a {dados_treino['Date'].max().strftime('%Y-%m-%d')})")
    print(f"   • Dados de teste: {len(dados_teste)} registros ({dados_teste['Date'].min().strftime('%Y-%m-%d')} a {dados_teste['Date'].max().strftime('%Y-%m-%d')})")

    if len(dados_treino) == 0 or len(dados_teste) == 0:
        print("❌ Divisão temporal resultou em conjunto vazio")
        return None, None, None, None

    # Extrair features e targets
    X_train = dados_treino[feature_cols]
    X_test = dados_teste[feature_cols]
    y_train = y_ordenado[mask_treino]
    y_test = y_ordenado[mask_teste]

    return X_train, X_test, y_train, y_test

def treinar_classificador_binario(X, y_binary, feature_cols, dataset_completo=None):
    """
    Treina um classificador XGBoost binário para sinais de trading
    Classes: 0=Venda, 1=Compra (sem classe "Sem Ação")
    Usa threshold de probabilidade configurável para determinar sinais válidos
    Usa divisão temporal configurável para treino e teste
    """
    # Usar configurações do XGBoost
    use_scaler = config.get('xgboost.use_standard_scaler')
    model_params = config.get('xgboost.model_params').copy()

    # Configurar para classificação binária
    model_params['objective'] = 'binary:logistic'
    model_params['eval_metric'] = 'logloss'  # Binary cross-entropy loss

    # Fazer divisão temporal se dataset_completo estiver disponível
    if dataset_completo is not None and 'Date' in dataset_completo.columns:
        train_years = config.get('xgboost.temporal_split.train_years', 4)
        test_years = config.get('xgboost.temporal_split.test_years', 1)
        print(f"\n📅 Usando divisão temporal dos dados ({train_years} anos treino, {test_years} ano(s) teste)")
        X_train, X_test, y_train, y_test = dividir_dados_temporal(
            dataset_completo, feature_cols, y_binary
        )

        if X_train is None:
            print("❌ Erro na divisão temporal. Usando divisão aleatória como fallback.")
            # Fallback para divisão aleatória
            test_size = config.get('xgboost.test_size')
            X_train, X_test, y_train, y_test = train_test_split(
                X, y_binary, test_size=test_size, random_state=model_params['random_state'],
                stratify=y_binary
            )
    else:
        print("\n⚠️ Dataset completo não disponível. Usando divisão aleatória.")
        # Fallback para divisão aleatória
        test_size = config.get('xgboost.test_size')
        X_train, X_test, y_train, y_test = train_test_split(
            X, y_binary, test_size=test_size, random_state=model_params['random_state'],
            stratify=y_binary
        )

    # Normalizar features se configurado
    if use_scaler:
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)
        X_train_scaled = pd.DataFrame(X_train_scaled, columns=feature_cols)
        X_test_scaled = pd.DataFrame(X_test_scaled, columns=feature_cols)
    else:
        scaler = None
        X_train_scaled = X_train
        X_test_scaled = X_test

    # Obter threshold configurável
    prob_threshold = config.get('xgboost.probability_threshold')

    # Treinar classificador binário
    print("\n🚀 Treinando classificador binário XGBoost...")
    print(f"   • Classes: 0=Venda, 1=Compra (sem classe 'Sem Ação')")
    print(f"   • Função de perda: Binary cross-entropy (logloss)")
    print(f"   • Threshold de probabilidade: {prob_threshold} (se nenhuma classe > {prob_threshold}, sem sinal)")

    modelo = xgb.XGBClassifier(**model_params)
    modelo.fit(X_train_scaled, y_train)

    # Fazer predições
    y_pred_proba = modelo.predict_proba(X_test_scaled)

    # Aplicar threshold configurável para determinar sinais válidos
    # Se nenhuma classe tem probabilidade > threshold, não há sinal
    y_pred_with_threshold = []

    for prob in y_pred_proba:
        prob_venda = prob[0]  # Classe 0 = Venda
        prob_compra = prob[1]  # Classe 1 = Compra

        if prob_compra > prob_threshold and prob_compra > prob_venda:
            y_pred_with_threshold.append(1)  # Compra
        elif prob_venda > prob_threshold and prob_venda > prob_compra:
            y_pred_with_threshold.append(0)  # Venda
        else:
            # Se nenhuma classe tem probabilidade > threshold, usar a classe com maior probabilidade
            # mas marcar como "sem sinal forte"
            y_pred_with_threshold.append(1 if prob_compra > prob_venda else 0)

    y_pred = np.array(y_pred_with_threshold)

    # Calcular métricas
    accuracy = accuracy_score(y_test, y_pred)

    print(f"   • Acurácia geral: {accuracy:.3f}")
    print(f"   • Relatório de classificação:")
    print(classification_report(y_test, y_pred, target_names=['Venda', 'Compra']))

    # Estatísticas do threshold
    sinais_fortes = sum(1 for prob in y_pred_proba if max(prob) > prob_threshold)
    total_predicoes = len(y_pred_proba)
    print(f"   • Sinais com probabilidade > {prob_threshold}: {sinais_fortes}/{total_predicoes} ({sinais_fortes/total_predicoes*100:.1f}%)")

    resultados = {
        'modelo': modelo,
        'scaler': scaler,
        'accuracy': accuracy,
        'y_test': y_test,
        'y_pred': y_pred,
        'y_pred_proba': y_pred_proba,
        'feature_importance': modelo.feature_importances_,
        'classes': ['Venda', 'Compra'],
        'prob_threshold': prob_threshold
    }

    return resultados, feature_cols

def aplicar_predicoes_modelo_binario(acoes_dados, resultados_modelo, feature_cols):
    """
    Aplica as predições do modelo XGBoost binário aos dados de cada ação
    Usa threshold configurável para determinar sinais válidos
    """
    print(f"\n🔮 Aplicando predições do modelo binário aos dados...")

    modelo = resultados_modelo['modelo']
    scaler = resultados_modelo['scaler']
    prob_threshold = resultados_modelo['prob_threshold']

    acoes_com_predicoes = {}

    for ticker, dados in acoes_dados.items():
        if dados is not None and len(dados) > 0:
            dados_copy = dados.copy()

            # Preparar features para predição
            try:
                # Verificar se todas as features existem
                features_disponiveis = [col for col in feature_cols if col in dados_copy.columns]
                if len(features_disponiveis) != len(feature_cols):
                    print(f"     ⚠️ Features faltando para {ticker}: {len(features_disponiveis)}/{len(feature_cols)}")
                    continue

                X = dados_copy[feature_cols].dropna()
                if len(X) == 0:
                    continue

                # Aplicar scaler se necessário
                if scaler is not None:
                    X_scaled = scaler.transform(X)
                    X_scaled = pd.DataFrame(X_scaled, columns=feature_cols, index=X.index)
                else:
                    X_scaled = X

                # Fazer predições binárias
                pred_proba = modelo.predict_proba(X_scaled)

                # Aplicar threshold configurável para determinar sinais válidos
                threshold = config.get('xgboost.probability_threshold')
                pred_compra = []
                pred_venda = []
                pred_binary = []

                for prob in pred_proba:
                    prob_venda = prob[0]  # Classe 0 = Venda
                    prob_compra = prob[1]  # Classe 1 = Compra

                    if prob_compra > threshold and prob_compra > prob_venda:
                        pred_compra.append(1)
                        pred_venda.append(0)
                        pred_binary.append(1)
                    elif prob_venda > threshold and prob_venda > prob_compra:
                        pred_compra.append(0)
                        pred_venda.append(1)
                        pred_binary.append(0)
                    else:
                        # Sem sinal forte - não compra nem venda
                        pred_compra.append(0)
                        pred_venda.append(0)
                        pred_binary.append(1 if prob_compra > prob_venda else 0)  # Para compatibilidade

                # Adicionar predições aos dados
                dados_copy.loc[X.index, 'Pred_Binary'] = pred_binary
                dados_copy.loc[X.index, 'Pred_Compra'] = pred_compra
                dados_copy.loc[X.index, 'Pred_Venda'] = pred_venda

                # Adicionar probabilidades
                dados_copy.loc[X.index, 'Prob_Venda'] = pred_proba[:, 0]
                dados_copy.loc[X.index, 'Prob_Compra'] = pred_proba[:, 1]

                # Preencher NaN com valores padrão
                dados_copy['Pred_Binary'] = dados_copy['Pred_Binary'].fillna(0).astype(int)
                dados_copy['Pred_Compra'] = dados_copy['Pred_Compra'].fillna(0).astype(int)
                dados_copy['Pred_Venda'] = dados_copy['Pred_Venda'].fillna(0).astype(int)
                dados_copy['Prob_Compra'] = dados_copy['Prob_Compra'].fillna(0.0)
                dados_copy['Prob_Venda'] = dados_copy['Prob_Venda'].fillna(0.0)

                acoes_com_predicoes[ticker] = dados_copy

            except Exception as e:
                print(f"     ❌ Erro ao aplicar predições para {ticker}: {e}")
                acoes_com_predicoes[ticker] = dados_copy

    print(f"   ✅ Predições aplicadas a {len(acoes_com_predicoes)} ações")
    threshold = config.get('xgboost.probability_threshold')
    print(f"   🎯 Threshold usado: {threshold} (sinais só são gerados se probabilidade > {threshold})")
    return acoes_com_predicoes

def criar_graficos_estruturados(resultados, feature_cols, acoes_dados):
    """
    Cria gráficos organizados seguindo estrutura MM/Butterworth
    """
    print(f"\n📊 Criando gráficos estruturados...")

    # Criar diretórios
    figures_dir = 'results/figures/xgboost_analysis'
    os.makedirs(figures_dir, exist_ok=True)

    # 1. Gráfico principal dos resultados do modelo
    criar_grafico_principal_modelo(resultados, feature_cols, figures_dir)

    # 2. Gráficos individuais por ação (amostra)
    criar_graficos_individuais_acoes(acoes_dados, figures_dir)

    print(f"   ✅ Gráficos salvos em: {figures_dir}/")

def criar_grafico_principal_modelo(resultados, feature_cols, figures_dir):
    """
    Cria o gráfico principal com resultados do modelo binário
    """
    # Configurar estilo
    plt.style.use('default')

    # Criar figura com subplots
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    threshold = resultados['prob_threshold']
    fig.suptitle(f'Classificador XGBoost Binário - Sinais de Trading (Threshold {threshold})', fontsize=16, fontweight='bold')

    # Matriz de confusão - Modelo binário
    cm_binary = confusion_matrix(resultados['y_test'], resultados['y_pred'])
    sns.heatmap(cm_binary, annot=True, fmt='d', cmap='viridis', ax=axes[0,0])
    axes[0,0].set_title(f'Matriz de Confusão - Modelo Binário\nAcurácia: {resultados["accuracy"]:.3f}')
    axes[0,0].set_xlabel('Predito')
    axes[0,0].set_ylabel('Real')
    axes[0,0].set_xticklabels(['Venda', 'Compra'])
    axes[0,0].set_yticklabels(['Venda', 'Compra'])

    # Distribuição das classes reais vs preditas
    y_test_counts = np.bincount(resultados['y_test'], minlength=2)
    y_pred_counts = np.bincount(resultados['y_pred'], minlength=2)

    x = np.arange(2)
    width = 0.35
    class_names = ['Venda', 'Compra']

    bars1 = axes[0,1].bar(x - width/2, y_test_counts, width, label='Real', alpha=0.8, color='lightblue')
    bars2 = axes[0,1].bar(x + width/2, y_pred_counts, width, label='Predito', alpha=0.8, color='orange')

    axes[0,1].set_title('Distribuição Real vs Predita')
    axes[0,1].set_ylabel('Quantidade')
    axes[0,1].set_xticks(x)
    axes[0,1].set_xticklabels(class_names)
    axes[0,1].legend()

    # Adicionar valores nas barras
    for bars in [bars1, bars2]:
        for bar in bars:
            height = bar.get_height()
            axes[0,1].text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                          f'{int(height)}', ha='center', va='bottom')

    # Feature importance
    importance_df = pd.DataFrame({
        'feature': feature_cols,
        'importance': resultados['feature_importance']
    }).sort_values('importance', ascending=True).tail(10)

    axes[1,0].barh(importance_df['feature'], importance_df['importance'], color='lightgreen')
    axes[1,0].set_title('Top 10 Features - Modelo Binário')
    axes[1,0].set_xlabel('Importância')

    # Probabilidades médias por classe
    prob_medias = np.mean(resultados['y_pred_proba'], axis=0)
    colors = ['red', 'green']

    bars = axes[1,1].bar(class_names, prob_medias, color=colors, alpha=0.7)
    axes[1,1].set_title(f'Probabilidade Média por Classe\n(Threshold: {resultados["prob_threshold"]})')
    axes[1,1].set_ylabel('Probabilidade Média')
    axes[1,1].set_ylim(0, 1)

    # Adicionar linha do threshold
    axes[1,1].axhline(y=resultados['prob_threshold'], color='black', linestyle='--', alpha=0.7, label=f'Threshold {resultados["prob_threshold"]}')
    axes[1,1].legend()

    # Adicionar valores nas barras
    for bar, prob in zip(bars, prob_medias):
        axes[1,1].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                      f'{prob:.3f}', ha='center', va='bottom')

    plt.tight_layout()

    # Salvar gráfico principal
    nome_arquivo = os.path.join(figures_dir, 'xgboost_modelo_resultados.png')
    plt.savefig(nome_arquivo, dpi=300, bbox_inches='tight')
    plt.close()

def criar_graficos_individuais_acoes(acoes_dados, figures_dir):
    """
    Cria gráficos individuais para TODAS as ações analisadas
    """
    print(f"   📊 Criando gráficos individuais para {len(acoes_dados)} ações...")

    # Criar subdiretório para gráficos individuais
    individual_dir = os.path.join(figures_dir, 'individual_stocks')
    os.makedirs(individual_dir, exist_ok=True)

    # Carregar informações da carteira
    try:
        acoes_carteira = carregar_acoes_carteira()
        carteira_info = {ticker: (nome, qtd) for ticker, nome, qtd in acoes_carteira}
    except:
        carteira_info = {}

    graficos_criados = 0

    for ticker in acoes_dados.keys():
        dados = acoes_dados[ticker]
        if dados is not None and len(dados) > 50:  # Mínimo de 50 dias de dados
            try:
                criar_grafico_individual_acao(ticker, dados, individual_dir, carteira_info)
                graficos_criados += 1
            except Exception as e:
                print(f"     ⚠️ Erro ao criar gráfico para {ticker}: {e}")

    print(f"   ✅ {graficos_criados} gráficos individuais criados em: {individual_dir}/")

def criar_grafico_individual_acao(ticker, dados, figures_dir, carteira_info):
    """
    Cria gráfico individual para uma ação com informações da carteira
    """
    ticker_clean = ticker.replace('.SA', '')

    # Verificar se está na carteira
    quantidade_carteira = carteira_info.get(ticker, (None, 0))[1]
    na_carteira = quantidade_carteira > 0

    # Configurar figura com 3 subplots
    fig, axes = plt.subplots(3, 1, figsize=(14, 12))

    # Título do gráfico - incluir informação da carteira
    if na_carteira:
        titulo_base = f'XGBoost - {ticker_clean} - {quantidade_carteira:.0f} ações na carteira'
    else:
        titulo_base = f'XGBoost - {ticker_clean} - SEM POSIÇÃO'

    fig.suptitle(titulo_base, fontsize=16, fontweight='bold')

    # Últimos 12 meses para visualização (ou todos os dados se menos)
    dados_recentes = dados.tail(min(252, len(dados)))  # 252 dias úteis ≈ 1 ano

    # Gráfico 1: Preço e Sinais de Trading
    ax1 = axes[0]
    ax1.plot(dados_recentes.index, dados_recentes['Media_OHLC'],
             label='Média OHLC', color='blue', linewidth=2)

    # Marcar sinais de compra (usar predições se disponíveis, senão usar sinais de treinamento)
    coluna_compra = 'Pred_Compra' if 'Pred_Compra' in dados_recentes.columns else 'Sinal_Compra'
    coluna_venda = 'Pred_Venda' if 'Pred_Venda' in dados_recentes.columns else 'Sinal_Venda'

    sinais_compra = dados_recentes[dados_recentes[coluna_compra] == 1]
    if len(sinais_compra) > 0:
        label_compra = f'Predição Compra ({len(sinais_compra)})' if 'Pred_Compra' in dados_recentes.columns else f'Sinal Compra ({len(sinais_compra)})'
        ax1.scatter(sinais_compra.index, sinais_compra['Media_OHLC'],
                   color='green', marker='^', s=80, label=label_compra,
                   alpha=0.8, edgecolors='darkgreen', linewidth=1)

    # Marcar sinais de venda (usar predições se disponíveis)
    sinais_venda = dados_recentes[dados_recentes[coluna_venda] == 1]
    if len(sinais_venda) > 0:
        label_venda = f'Predição Venda ({len(sinais_venda)})' if 'Pred_Venda' in dados_recentes.columns else f'Sinal Venda ({len(sinais_venda)})'
        ax1.scatter(sinais_venda.index, sinais_venda['Media_OHLC'],
                   color='red', marker='v', s=80, label=label_venda,
                   alpha=0.8, edgecolors='darkred', linewidth=1)

    # Destacar último sinal se houver (usar predições)
    ultimo_dia = dados_recentes.iloc[-1]
    if ultimo_dia[coluna_compra] == 1:
        label_hoje = '🟢 MODELO PREDIZ: COMPRAR HOJE' if 'Pred_Compra' in dados_recentes.columns else '🟢 COMPRAR HOJE'
        ax1.scatter(ultimo_dia.name, ultimo_dia['Media_OHLC'],
                   color='lime', marker='^', s=150, label=label_hoje,
                   alpha=1.0, edgecolors='darkgreen', linewidth=2)
    elif ultimo_dia[coluna_venda] == 1:
        label_hoje = '🔴 MODELO PREDIZ: VENDER HOJE' if 'Pred_Venda' in dados_recentes.columns else '🔴 VENDER HOJE'
        ax1.scatter(ultimo_dia.name, ultimo_dia['Media_OHLC'],
                   color='orangered', marker='v', s=150, label=label_hoje,
                   alpha=1.0, edgecolors='darkred', linewidth=2)

    ax1.set_title('Preço e Sinais de Trading XGBoost', fontsize=12, fontweight='bold')
    ax1.set_ylabel('Preço (R$)', fontsize=11)
    ax1.legend(loc='upper left', fontsize=10)
    ax1.grid(True, alpha=0.3)

    # Gráfico 2: Volume
    ax2 = axes[1]
    ax2.bar(dados_recentes.index, dados_recentes['Volume'],
            alpha=0.6, color='steelblue', label='Volume')

    # Destacar volume nos dias de sinais
    if len(sinais_compra) > 0:
        ax2.bar(sinais_compra.index, sinais_compra['Volume'],
                alpha=0.8, color='green', label='Volume - Compra')
    if len(sinais_venda) > 0:
        ax2.bar(sinais_venda.index, sinais_venda['Volume'],
                alpha=0.8, color='red', label='Volume - Venda')

    ax2.set_title('Volume de Negociação', fontsize=12, fontweight='bold')
    ax2.set_ylabel('Volume', fontsize=11)
    ax2.legend(loc='upper left', fontsize=10)
    ax2.grid(True, alpha=0.3)

    # Gráfico 3: Volatilidade e Spread
    ax3 = axes[2]
    ax3_twin = ax3.twinx()

    # Volatilidade
    ax3.plot(dados_recentes.index, dados_recentes['Volatilidade'],
             color='orange', linewidth=2, label='Volatilidade', alpha=0.8)

    # Spread (se disponível)
    if 'Spread' in dados_recentes.columns:
        ax3_twin.plot(dados_recentes.index, dados_recentes['Spread'],
                     color='purple', linewidth=2, label='Spread', alpha=0.8)
        ax3_twin.set_ylabel('Spread', fontsize=11, color='purple')
        ax3_twin.legend(loc='upper right', fontsize=10)

    ax3.set_title('Volatilidade e Spread', fontsize=12, fontweight='bold')
    ax3.set_ylabel('Volatilidade', fontsize=11, color='orange')
    ax3.set_xlabel('Data', fontsize=11)
    ax3.legend(loc='upper left', fontsize=10)
    ax3.grid(True, alpha=0.3)

    # Ajustar layout
    plt.tight_layout()

    # Salvar gráfico individual
    nome_arquivo = os.path.join(figures_dir, f'xgboost_{ticker_clean}.png')
    plt.savefig(nome_arquivo, dpi=300, bbox_inches='tight')
    plt.close()

def salvar_modelos_estruturado(resultados, feature_cols):
    """
    Salva o modelo binário treinado em estrutura organizada
    """
    print(f"\n💾 Salvando modelo binário...")

    # Criar diretório para modelos XGBoost
    modelo_dir = 'results/models/xgboost_analysis'
    os.makedirs(modelo_dir, exist_ok=True)

    # Remover modelos antigos se existirem
    old_files = ['modelo_sinais_compra.pkl', 'modelo_sinais_venda.pkl', 'modelo_multiclasse.pkl']
    for old_file in old_files:
        old_path = os.path.join(modelo_dir, old_file)
        if os.path.exists(old_path):
            os.remove(old_path)
            print(f"   🗑️ Removido modelo antigo: {old_file}")

    # Salvar modelo binário principal
    modelo_binary_path = os.path.join(modelo_dir, 'modelo_binario.pkl')
    with open(modelo_binary_path, 'wb') as f:
        pickle.dump({
            'modelo': resultados['modelo'],
            'scaler': resultados['scaler'],
            'feature_cols': feature_cols,
            'accuracy': resultados['accuracy'],
            'classes': ['Venda', 'Compra'],
            'prob_threshold': resultados['prob_threshold'],
            'config_usado': {
                'data_period': config.get('xgboost.data_period'),
                'signal_horizon': config.get('xgboost.signal_horizon'),
                'ohlc_lags': config.get('xgboost.features.ohlc_lags'),
                'model_params': config.get('xgboost.model_params')
            }
        }, f)

    # Salvar resumo detalhado dos resultados
    resumo_path = os.path.join(modelo_dir, 'resumo_treinamento.txt')
    with open(resumo_path, 'w') as f:
        f.write("RESUMO DO TREINAMENTO XGBOOST BINÁRIO - SINAIS DE TRADING\n")
        f.write("=" * 70 + "\n\n")
        f.write(f"Data de treinamento: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

        f.write("MODELO BINÁRIO:\n")
        f.write("  • Tipo: XGBoost Binário (sem classe 'Sem Ação')\n")
        f.write("  • Classes: 0=Venda, 1=Compra\n")
        f.write("  • Função de perda: Binary cross-entropy (logloss)\n")
        f.write(f"  • Threshold de probabilidade: {resultados['prob_threshold']} (sinais só gerados se prob > {resultados['prob_threshold']})\n")
        f.write("  • Features básicas: pct_change da média OHLC (variação percentual), Volume, Spread, Volatilidade\n")
        f.write("  • Features econométricas: Parkinson, MFI, EMV, Amihud, Roll Spread,\n")
        f.write("    Hurst, Vol/Volume, CMF, A/D Line, Volume Oscillator (11 features)\n")
        f.write(f"  • Features lagged: {config.get('xgboost.features.econometric_lags')} lags para cada feature econométrica\n")
        f.write(f"  • Total de features: {len(feature_cols)}\n")
        f.write(f"  • Acurácia geral: {resultados['accuracy']:.3f}\n\n")

        f.write("CONFIGURAÇÕES UTILIZADAS:\n")
        f.write(f"  • Período de dados: {config.get('xgboost.data_period')}\n")
        f.write(f"  • Horizonte de sinais: {config.get('xgboost.signal_horizon')} dias\n")
        f.write(f"  • Lags OHLC: {config.get('xgboost.features.ohlc_lags')}\n")
        f.write(f"  • Lags features econométricas: {config.get('xgboost.features.econometric_lags')}\n")
        f.write(f"  • Janela volatilidade: {config.get('xgboost.features.volatility_window')}\n")
        f.write(f"  • Multiplicador spread: {config.get('xgboost.features.spread_multiplier')}\n\n")

        f.write(f"FEATURES UTILIZADAS ({len(feature_cols)}):\n")
        for i, feature in enumerate(feature_cols, 1):
            f.write(f"  {i:2d}. {feature}\n")

        f.write(f"\nRESULTADOS DO MODELO:\n")
        f.write(f"  • Acurácia Geral: {resultados['accuracy']:.3f}\n")

        # Estatísticas das predições
        unique, counts = np.unique(resultados['y_pred'], return_counts=True)
        class_stats = dict(zip(unique, counts))
        total_pred = len(resultados['y_pred'])

        f.write(f"  • Distribuição das Predições:\n")
        for classe, nome in enumerate(resultados['classes']):
            count = class_stats.get(classe, 0)
            f.write(f"    - {nome}: {count} ({count/total_pred*100:.1f}%)\n")
        f.write("\n")

        f.write(f"DEFINIÇÃO DOS SINAIS:\n")
        signal_horizon = config.get('xgboost.signal_horizon')
        f.write(f"  • Sinal de Compra: Média OHLC atual < Média OHLC {signal_horizon} dias à frente\n")
        f.write(f"  • Sinal de Venda: Média OHLC atual > Média OHLC {signal_horizon} dias à frente\n")
        f.write(f"  • Sem Ação: Casos onde não há sinal de compra nem venda\n\n")

        f.write(f"PARÂMETROS DO XGBOOST:\n")
        model_params = config.get('xgboost.model_params')
        for param, valor in model_params.items():
            f.write(f"  • {param}: {valor}\n")
        f.write(f"  • objective: multi:softprob (adicionado automaticamente)\n")
        f.write(f"  • num_class: 3 (adicionado automaticamente)\n")
        f.write(f"  • eval_metric: mlogloss (adicionado automaticamente)\n")

    print(f"   ✅ Modelo multiclasse salvo em: {modelo_dir}/")
    print(f"   📄 Resumo: {resumo_path}")

    return modelo_dir

def salvar_dados_csv_estruturado(acoes_dados, resultados_modelo=None):
    """
    Salva dados em estrutura organizada seguindo padrão MM/Butterworth
    Inclui predições do modelo se disponíveis
    """
    print(f"\n💾 Salvando dados em estrutura organizada...")

    # Criar diretórios
    csv_dir = 'results/csv/xgboost_analysis'
    individual_dir = os.path.join(csv_dir, 'individual_stocks')
    os.makedirs(csv_dir, exist_ok=True)
    os.makedirs(individual_dir, exist_ok=True)

    # Lista para dados completos
    dados_completos = []

    # Processar cada ação individualmente
    for ticker, dados in acoes_dados.items():
        if dados is not None and len(dados) > 0:
            # Preparar dados para CSV
            dados_csv = dados.copy()
            ticker_clean = ticker.replace('.SA', '')
            dados_csv['Ticker'] = ticker_clean

            # Selecionar colunas relevantes (incluindo predições e features econométricas se disponíveis)
            colunas_csv = [
                'Ticker', 'Media_OHLC', 'Volume', 'Spread', 'Volatilidade',
                'Sinal_Compra', 'Sinal_Venda', 'Media_OHLC_Futura'
            ]

            # Adicionar features econométricas se existirem (excluindo GK_Volatility)
            econometric_features = [
                'Parkinson_Volatility', 'MFI', 'EMV', 'EMV_MA',
                'Amihud', 'Roll_Spread', 'Hurst', 'Vol_per_Volume', 'CMF', 'AD_Line', 'VO'
            ]

            for feature in econometric_features:
                if feature in dados_csv.columns:
                    colunas_csv.append(feature)

            # Adicionar features lagged se existirem
            econometric_lags = config.get('xgboost.features.econometric_lags')
            econometric_features_all = [
                'Volume', 'Spread', 'Volatilidade',
                'Parkinson_Volatility', 'MFI', 'EMV', 'EMV_MA',
                'Amihud', 'Roll_Spread', 'Hurst', 'Vol_per_Volume', 'CMF', 'AD_Line', 'VO'
            ]

            for feature in econometric_features_all:
                for i in range(1, econometric_lags + 1):
                    lagged_feature = f'{feature}_Lag_{i}'
                    if lagged_feature in dados_csv.columns:
                        colunas_csv.append(lagged_feature)

            # Adicionar colunas de predição binária se existirem
            if 'Pred_Binary' in dados_csv.columns:
                colunas_csv.append('Pred_Binary')
            if 'Pred_Compra' in dados_csv.columns:
                colunas_csv.append('Pred_Compra')
            if 'Pred_Venda' in dados_csv.columns:
                colunas_csv.append('Pred_Venda')

            # Adicionar probabilidades (sempre incluir se existirem)
            if 'Prob_Compra' in dados_csv.columns:
                colunas_csv.append('Prob_Compra')
            if 'Prob_Venda' in dados_csv.columns:
                colunas_csv.append('Prob_Venda')

            # Verificar se todas as colunas existem
            colunas_existentes = [col for col in colunas_csv if col in dados_csv.columns]

            if len(colunas_existentes) >= 6:  # Pelo menos as colunas principais
                dados_selecionados = dados_csv[colunas_existentes].copy()
                dados_selecionados.reset_index(inplace=True)
                dados_selecionados['Data'] = dados_selecionados['Date'].dt.strftime('%Y-%m-%d')
                dados_selecionados.drop('Date', axis=1, inplace=True)

                # Reordenar colunas
                cols_ordenadas = ['Ticker', 'Data'] + [col for col in colunas_existentes if col != 'Ticker']
                dados_selecionados = dados_selecionados[cols_ordenadas]

                # Salvar arquivo individual
                arquivo_individual = os.path.join(individual_dir, f'xgboost_{ticker_clean}.csv')
                dados_selecionados.to_csv(arquivo_individual, index=False)

                dados_completos.append(dados_selecionados)

    if dados_completos:
        # Combinar todos os dados
        df_final = pd.concat(dados_completos, ignore_index=True)
        df_final = df_final.sort_values(['Ticker', 'Data'])

        # Salvar arquivo completo principal
        csv_completo_path = os.path.join(csv_dir, 'resultados_xgboost_completo.csv')
        df_final.to_csv(csv_completo_path, index=False)

        # Criar resumo por ação
        resumo_por_acao = df_final.groupby('Ticker').agg({
            'Sinal_Compra': 'sum',
            'Sinal_Venda': 'sum',
            'Media_OHLC': ['first', 'last', 'mean'],
            'Volume': 'mean',
            'Volatilidade': 'mean',
            'Data': 'count'
        }).round(2)

        # Flatten column names
        resumo_por_acao.columns = ['Sinais_Compra', 'Sinais_Venda', 'Preco_Inicial', 'Preco_Final', 'Preco_Medio', 'Volume_Medio', 'Volatilidade_Media', 'Total_Dias']
        resumo_por_acao['Performance_%'] = ((resumo_por_acao['Preco_Final'] / resumo_por_acao['Preco_Inicial']) - 1) * 100
        resumo_por_acao['Total_Sinais'] = resumo_por_acao['Sinais_Compra'] + resumo_por_acao['Sinais_Venda']

        # Salvar resumo
        resumo_path = os.path.join(csv_dir, 'resultados_xgboost.csv')
        resumo_por_acao.to_csv(resumo_path)

        print(f"   ✅ Dados salvos em: {csv_dir}/")
        print(f"   📊 Total de registros: {len(df_final):,}")
        print(f"   📈 Ações processadas: {df_final['Ticker'].nunique()}")
        print(f"   📁 Arquivos individuais: {individual_dir}/")

        # Estatísticas dos sinais
        total_compra = df_final['Sinal_Compra'].sum()
        total_venda = df_final['Sinal_Venda'].sum()
        total_registros = len(df_final)

        print(f"   🟢 Sinais de Compra: {total_compra:,} ({total_compra/total_registros*100:.1f}%)")
        print(f"   🔴 Sinais de Venda: {total_venda:,} ({total_venda/total_registros*100:.1f}%)")

        return csv_completo_path
    else:
        print(f"   ❌ Nenhum dado válido para salvar")
        return None

def carregar_acoes_carteira():
    """
    Carrega as ações da carteira do arquivo CSV usando configuração
    Filtra apenas ações com quantidade líquida > 0 (ainda na carteira)
    """
    try:
        # Usar configuração ao invés de hardcode
        file_paths = config.get_file_paths()
        csv_path = file_paths.get('carteira', 'carteira.csv')
        df = pd.read_csv(csv_path)

        # Pegar todas as ações da carteira e calcular quantidade líquida
        acoes = []
        tickers_processados = set()

        for _, row in df.iterrows():
            if pd.notna(row['ticker']) and row['ticker'].strip():
                ticker = row['ticker'].strip()

                # Evitar duplicatas
                if ticker in tickers_processados:
                    continue
                tickers_processados.add(ticker)

                # Calcular quantidade líquida (compras - vendas)
                quantidade_liquida = obter_quantidade_carteira(ticker)

                # Incluir apenas ações com quantidade > 0 (ainda na carteira)
                if quantidade_liquida > 0:
                    # Extrair nome da empresa do ticker (simplificado)
                    nome = ticker.replace('.SA', '')
                    acoes.append((ticker, nome, quantidade_liquida))

        return acoes

    except Exception as e:
        print(f"❌ Erro ao carregar arquivo da carteira: {e}")
        return []

def obter_quantidade_carteira(ticker):
    """
    Obtém a quantidade líquida de ações de um ticker na carteira usando configuração
    Valores negativos na quantidade indicam vendas
    """
    try:
        # Usar configuração ao invés de hardcode
        file_paths = config.get_file_paths()
        csv_path = file_paths.get('carteira', 'carteira.csv')
        df = pd.read_csv(csv_path)

        # Filtrar por ticker e somar quantidades (compras - vendas)
        ticker_data = df[df['ticker'] == ticker]
        if not ticker_data.empty:
            quantidade_liquida = ticker_data['quantidade'].sum()
            return quantidade_liquida
        else:
            return 0

    except Exception as e:
        print(f"❌ Erro ao obter quantidade da carteira para {ticker}: {e}")
        return 0

def imprimir_recomendacoes_ultimo_dia(acoes_dados):
    """
    Imprime recomendações de compra e venda para o último dia de dados
    Similar ao formato do análise Butterworth
    """
    print(f"\n📊 ESTRATÉGIA DE TRADING - CLASSIFICADOR XGBOOST")
    print(f"=" * 60)
    print(f"🎯 Sinais detectados para o último dia disponível:")

    # Carregar informações da carteira
    try:
        acoes_carteira = carregar_acoes_carteira()
        tickers_carteira = {ticker for ticker, _, _ in acoes_carteira}
        carteira_info = {ticker: (nome, qtd) for ticker, nome, qtd in acoes_carteira}
    except:
        tickers_carteira = set()
        carteira_info = {}

    sinais_compra = []
    sinais_venda = []

    for ticker, dados in acoes_dados.items():
        if dados is not None and len(dados) > 0:
            # Usar predições se disponíveis, senão usar sinais de treinamento
            if 'Pred_Compra' in dados.columns and 'Pred_Venda' in dados.columns:
                colunas_necessarias = ['Pred_Compra', 'Pred_Venda']
                coluna_compra = 'Pred_Compra'
                coluna_venda = 'Pred_Venda'
                tipo_sinal = "PREDIÇÃO"
            else:
                colunas_necessarias = ['Sinal_Compra', 'Sinal_Venda']
                coluna_compra = 'Sinal_Compra'
                coluna_venda = 'Sinal_Venda'
                tipo_sinal = "SINAL"

            # Pegar o último dia com dados válidos (não NaN)
            dados_validos = dados.dropna(subset=colunas_necessarias)

            if len(dados_validos) > 0:
                ultimo_dia = dados_validos.iloc[-1]
                ticker_clean = ticker.replace('.SA', '')
                nome_empresa = ticker_clean  # Nome simplificado

                # Verificar sinais de compra (usar predições se disponíveis)
                if ultimo_dia[coluna_compra] == 1:
                    sinais_compra.append({
                        'ticker': ticker,
                        'ticker_clean': ticker_clean,
                        'nome': nome_empresa,
                        'preco': ultimo_dia['Media_OHLC'],
                        'volume': ultimo_dia.get('Volume', 0),
                        'volatilidade': ultimo_dia.get('Volatilidade', 0),
                        'spread': ultimo_dia.get('Spread', 0),
                        'data': ultimo_dia.name,
                        'na_carteira': ticker in tickers_carteira,
                        'quantidade_carteira': carteira_info.get(ticker, (None, 0))[1],
                        'prob_compra': ultimo_dia.get('Prob_Compra', 0),
                        'prob_venda': ultimo_dia.get('Prob_Venda', 0)
                    })

                # Verificar sinais de venda (APENAS para ações da carteira na exibição)
                if ultimo_dia[coluna_venda] == 1 and ticker in tickers_carteira:
                    sinais_venda.append({
                        'ticker': ticker,
                        'ticker_clean': ticker_clean,
                        'nome': nome_empresa,
                        'preco': ultimo_dia['Media_OHLC'],
                        'volume': ultimo_dia.get('Volume', 0),
                        'volatilidade': ultimo_dia.get('Volatilidade', 0),
                        'spread': ultimo_dia.get('Spread', 0),
                        'data': ultimo_dia.name,
                        'na_carteira': ticker in tickers_carteira,
                        'quantidade_carteira': carteira_info.get(ticker, (None, 0))[1],
                        'prob_compra': ultimo_dia.get('Prob_Compra', 0),
                        'prob_venda': ultimo_dia.get('Prob_Venda', 0)
                    })

    # Verificar se há sinais
    if not sinais_compra and not sinais_venda:
        print("✅ Nenhum sinal de compra ou venda detectado para o último dia.")
        return

    # Exibir sinais de compra
    if sinais_compra:
        print(f"\n🟢 SINAIS DE COMPRA ({len(sinais_compra)} ações):")
        print("-" * 60)
        for sinal in sorted(sinais_compra, key=lambda x: x['ticker_clean']):
            data_str = pd.to_datetime(sinal['data']).strftime('%d/%m/%Y') if pd.notna(sinal['data']) else 'N/A'
            print(f"   📈 {sinal['ticker_clean']} ({sinal['nome'][:30]})")
            print(f"      💰 Preço: R$ {sinal['preco']:.2f}")
            print(f"      📅 Data: {data_str}")
            print(f"      📊 Volume: {sinal['volume']:,.0f}")
            if sinal['volatilidade'] > 0:
                print(f"      📈 Volatilidade: {sinal['volatilidade']:.4f}")
            if sinal['spread'] > 0:
                print(f"      💹 Spread: {sinal['spread']:.4f}")
            print(f"      🎯 Sinal: XGBoost prevê alta no preço")
            # Mostrar probabilidades do modelo
            if sinal['prob_compra'] > 0 or sinal['prob_venda'] > 0:
                print(f"      🤖 Probabilidades: Compra {sinal['prob_compra']:.1%} | Venda {sinal['prob_venda']:.1%}")
            if sinal['na_carteira']:
                print(f"      ✅ VOCÊ JÁ POSSUI: {sinal['quantidade_carteira']:.0f} ações")
            print()

    # Exibir sinais de venda
    if sinais_venda:
        print(f"\n🔴 SINAIS DE VENDA ({len(sinais_venda)} ações):")
        print("-" * 60)

        # Separar ações da carteira das demais
        vendas_carteira = [s for s in sinais_venda if s['na_carteira']]
        vendas_outras = [s for s in sinais_venda if not s['na_carteira']]

        if vendas_carteira:
            print("   🎯 AÇÕES DA SUA CARTEIRA:")
            for sinal in sorted(vendas_carteira, key=lambda x: x['ticker_clean']):
                data_str = pd.to_datetime(sinal['data']).strftime('%d/%m/%Y') if pd.notna(sinal['data']) else 'N/A'
                print(f"   📉 {sinal['ticker_clean']} ({sinal['nome'][:30]}) ⚠️ VOCÊ POSSUI")
                print(f"      💰 Preço: R$ {sinal['preco']:.2f}")
                print(f"      📅 Data: {data_str}")
                print(f"      📊 Volume: {sinal['volume']:,.0f}")
                if sinal['volatilidade'] > 0:
                    print(f"      📈 Volatilidade: {sinal['volatilidade']:.4f}")
                if sinal['spread'] > 0:
                    print(f"      💹 Spread: {sinal['spread']:.4f}")
                print(f"      🎯 Sinal: XGBoost prevê queda no preço")
                # Mostrar probabilidades do modelo
                if sinal['prob_compra'] > 0 or sinal['prob_venda'] > 0:
                    print(f"      🤖 Probabilidades: Compra {sinal['prob_compra']:.1%} | Venda {sinal['prob_venda']:.1%}")
                print(f"      🏦 Quantidade na carteira: {sinal['quantidade_carteira']:.0f} ações")
                print()

        if vendas_outras:
            print("   📊 OUTRAS AÇÕES:")
            for sinal in sorted(vendas_outras, key=lambda x: x['ticker_clean']):
                data_str = pd.to_datetime(sinal['data']).strftime('%d/%m/%Y') if pd.notna(sinal['data']) else 'N/A'
                print(f"   📉 {sinal['ticker_clean']} ({sinal['nome'][:30]})")
                print(f"      💰 Preço: R$ {sinal['preco']:.2f}")
                print(f"      📅 Data: {data_str}")
                print(f"      📊 Volume: {sinal['volume']:,.0f}")
                if sinal['volatilidade'] > 0:
                    print(f"      📈 Volatilidade: {sinal['volatilidade']:.4f}")
                if sinal['spread'] > 0:
                    print(f"      💹 Spread: {sinal['spread']:.4f}")
                print(f"      🎯 Sinal: XGBoost prevê queda no preço")
                # Mostrar probabilidades do modelo
                if sinal['prob_compra'] > 0 or sinal['prob_venda'] > 0:
                    print(f"      🤖 Probabilidades: Compra {sinal['prob_compra']:.1%} | Venda {sinal['prob_venda']:.1%}")
                print()

    # Resumo final
    print("📋 RESUMO DOS SINAIS:")
    print(f"   🟢 Compra: {len(sinais_compra)} ações")
    print(f"   🔴 Venda: {len(sinais_venda)} ações")
    if sinais_venda:
        vendas_carteira = [s for s in sinais_venda if s['na_carteira']]
        if vendas_carteira:
            print(f"   ⚠️  Vendas na sua carteira: {len(vendas_carteira)} ações")
    print(f"   📊 Total de sinais: {len(sinais_compra) + len(sinais_venda)} ações")

def main():
    """
    Função principal
    """
    # Configurar ambiente
    setup_environment()
    
    print("🤖 CLASSIFICADOR XGBOOST BINÁRIO - SINAIS DE TRADING")
    print("=" * 80)
    print("📊 Baseado no pct_change da média OHLC das ações diversificadas")
    print("🎯 Modelo binário: apenas classes Compra/Venda (sem 'Sem Ação')")
    threshold = config.get('xgboost.probability_threshold')
    print(f"🔒 Threshold de probabilidade: {threshold} (sinais só gerados se prob > {threshold})")

    # Mostrar configurações
    signal_horizon = config.get('xgboost.signal_horizon')
    ohlc_lags = config.get('xgboost.features.ohlc_lags')
    data_period = config.get('xgboost.data_period')

    print(f"🎯 Sinais: Compra/Venda baseados em {signal_horizon} dias à frente")
    print(f"🔧 Features básicas: pct_change da Média OHLC passada ({ohlc_lags} dias), Volume, Spread, Volatilidade")
    print(f"🔬 Features econométricas: Parkinson, MFI, EMV, Amihud, Roll Spread,")
    print(f"   Hurst, Vol/Volume, CMF, A/D Line, Volume Oscillator (11 features)")
    print(f"⚠️  GK_Volatility removida (alta correlação com Parkinson_Volatility)")
    print(f"📅 Período de dados: {data_period}")
    print("=" * 80)
    
    # Carregar listas de ações
    acoes_diversificadas = carregar_acoes_diversificadas()
    acoes_carteira = carregar_acoes_carteira()

    # Combinar todas as ações, evitando duplicatas
    todas_acoes = []
    tickers_vistos = set()

    # Adicionar ações diversificadas
    for ticker, nome in acoes_diversificadas:
        if ticker not in tickers_vistos:
            todas_acoes.append((ticker, nome, "Diversificada"))
            tickers_vistos.add(ticker)

    # Adicionar ações da carteira
    for ticker, nome, quantidade in acoes_carteira:
        if ticker not in tickers_vistos:
            todas_acoes.append((ticker, nome, "Carteira"))
            tickers_vistos.add(ticker)
        else:
            # Marcar como ambas se já existe
            for i, (t, n, origem) in enumerate(todas_acoes):
                if t == ticker:
                    todas_acoes[i] = (t, n, "Diversificada + Carteira")
                    break

    if not todas_acoes:
        print("❌ Não foi possível carregar ações dos arquivos CSV")
        return

    print(f"\n📋 Serão analisadas {len(todas_acoes)} ações no total:")
    print(f"   • Ações diversificadas: {len(acoes_diversificadas)}")
    print(f"   • Ações da carteira: {len(acoes_carteira)}")
    print(f"   • Total único: {len(todas_acoes)}")

    # Criar set de tickers da carteira para verificação de sinais de venda
    tickers_carteira = {ticker for ticker, _, _ in acoes_carteira}
    
    # Baixar dados e calcular features
    print(f"\n📥 Baixando dados de {len(todas_acoes)} ações...")
    acoes_dados = {}

    for ticker, nome, origem in todas_acoes:
        dados = baixar_dados_acao(ticker, nome)
        if dados is not None:
            dados_processados = calcular_features_e_sinais(dados, ticker, tickers_carteira)
            acoes_dados[ticker] = dados_processados
    
    print(f"✅ Processadas {len(acoes_dados)} ações com sucesso")

    # Salvar sinais em CSV
    salvar_dados_csv_estruturado(acoes_dados)

    # Preparar dataset
    print(f"\n🔧 Preparando dataset...")
    X, y_binary, feature_cols, dataset_completo = preparar_dataset(acoes_dados)

    if X is None:
        print("❌ Erro ao preparar dataset")
        return

    # Treinar classificador binário com divisão temporal
    resultados, feature_cols = treinar_classificador_binario(X, y_binary, feature_cols, dataset_completo)

    if resultados is None:
        print("❌ Erro no treinamento do classificador")
        return

    # Aplicar predições do modelo aos dados
    acoes_com_predicoes = aplicar_predicoes_modelo_binario(acoes_dados, resultados, feature_cols)

    # Salvar dados com predições em CSV
    print(f"\n💾 Salvando dados com predições...")
    salvar_dados_csv_estruturado(acoes_com_predicoes)

    # Criar gráficos usando predições
    print(f"\n📊 Criando gráficos com predições do modelo...")
    criar_graficos_estruturados(resultados, feature_cols, acoes_com_predicoes)

    # Salvar modelos
    print(f"\n💾 Salvando modelos...")
    salvar_modelos_estruturado(resultados, feature_cols)

    # Imprimir recomendações para o último dia usando predições
    imprimir_recomendacoes_ultimo_dia(acoes_com_predicoes)

    print(f"\n✅ Análise concluída!")
    print(f"📊 Resultados do Modelo Binário:")
    print(f"   • Acurácia Geral: {resultados['accuracy']:.3f}")
    print(f"   • Classes: {resultados['classes']}")
    print(f"   • Threshold de probabilidade: {resultados['prob_threshold']}")

    # Estatísticas das predições
    unique, counts = np.unique(resultados['y_pred'], return_counts=True)
    class_stats = dict(zip(unique, counts))
    total_pred = len(resultados['y_pred'])

    print(f"   • Distribuição das Predições:")
    for classe, nome in enumerate(resultados['classes']):
        count = class_stats.get(classe, 0)
        print(f"     - {nome}: {count} ({count/total_pred*100:.1f}%)")

    # Estatísticas das probabilidades
    prob_medias = np.mean(resultados['y_pred_proba'], axis=0)
    print(f"   • Probabilidades médias:")
    for i, classe in enumerate(resultados['classes']):
        print(f"     - {classe}: {prob_medias[i]:.3f}")

    # Sinais com alta confiança (probabilidade > threshold)
    sinais_fortes = sum(1 for prob in resultados['y_pred_proba'] if max(prob) > resultados['prob_threshold'])
    total_predicoes = len(resultados['y_pred_proba'])
    print(f"   • Sinais com alta confiança (prob > {resultados['prob_threshold']}): {sinais_fortes}/{total_predicoes} ({sinais_fortes/total_predicoes*100:.1f}%)")

    print(f"\n💾 Probabilidades salvas nos arquivos CSV:")
    print(f"   • Prob_Compra: Probabilidade de sinal de compra")
    print(f"   • Prob_Venda: Probabilidade de sinal de venda")
    print(f"   • Sinais só são gerados quando probabilidade > {resultados['prob_threshold']}")

if __name__ == "__main__":
    main()
